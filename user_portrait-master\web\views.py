from web import models
from django.shortcuts import redirect
from django.shortcuts import render
from .Processing_data import plate_type_data,market_season_data,single_data,date_search_data,detail_data_process,group_data,pagination
from .MysqlConnect import connect

def login(request):
    conn = connect()
    if request.method == 'POST':
        username = request.POST.get('UserName')
        password = request.POST.get('PassWord')
        get_user = """select * from web_user_info where username=%s and password=%s ;"""
        get_url =  """select image from web_userbaseinfo where username =%s;"""
        cur = conn.cursor()
        cur1 = conn.cursor()
        cur.execute(get_user, (username, password))
        cur1.execute(get_url, username)
        all_users = cur.fetchall()
        all_url = cur1.fetchall()
        print(len(all_url))
        url_list = []
        if len(all_users) >= 1:
            if len(all_url) >= 1:
                if all_url[0][0] == None:
                    url_list = ''
                else:
                    url_list.append(all_url[0][0])
                    url_list = "".join(url_list)
            else:
                url_list = ''
            print(url_list)
            mark=all_users[0][4]
            if mark == 1:
                return redirect('/manage_user/')
            else:
                return render(request, "index.html", {"name": all_users[0][1],"url":url_list})
        elif len(all_users)==0 and len(username)!=0 and len(password)!=0 :
            return render(request, "login.html", {"error_msg": "账号或密码错误！请重新输入"})
    return render(request,'login.html')

def register(request):
    return render(request,'register.html')

def index(request):
    return render(request,'index.html')

def welcome(request):
    return render(request,'welcome.html')

def user_portrait(request):
    return render(request,'user_portrait.html')

def mail_data(request):
    sender =request.GET.get('username')
    if request.method == 'POST':
        title = request.POST.get('title')
        receiver = "管理员"
        email_context = request.POST.get('email_context')
        print(title,sender,receiver,email_context)
        models.mail.objects.create(title=title, sender=sender, receiver=receiver, email_context=email_context,
                                   email_mark="send")
        return render(request,'mail_compose.html')
    return render(request,'mail_compose.html',{"username":sender})

def mailbox(request):
    receiver = request.GET.get('username')
    conn=connect()
    cur=conn.cursor()
    mail_sql="""
            select * from web_mail where receiver = %s and email_mark ="receive";
            """
    cur.execute(mail_sql,receiver)
    mail_list=cur.fetchall()
    conn.close()
    return render(request, 'mailbox.html',{"mail_list":mail_list})

def mail_send(request):
    sender = request.GET.get('username')
    conn = connect()
    cur = conn.cursor()
    mail_sql = """
                select * from web_mail where sender = %s and email_mark ="send";
                """
    cur.execute(mail_sql, sender)
    mail_list = cur.fetchall()
    conn.close()
    return render(request, 'mail_send.html',{"mail_list":mail_list})

def answer(request):
    return render(request,'faq.html')

def season_count(request):
    list,sort=market_season_data.season_count()
    return render(request, 'season_count.html', {"list": list, "sort": sort})

def plate_type(request):
    plate_data = plate_type_data.plate_data_processing()[0]
    return render(request, 'plate_type.html', {"plate_data":plate_data})

def single_view(request):
    userid = request.GET.get('userid', '')
    wordcloud_data=single_data.get_userid_chart(userid)[0]
    creation_time = single_data.get_userid_chart(userid)[1]
    product_color = single_data.get_userid_chart(userid)[2]
    product_size = single_data.get_userid_chart(userid)[3]
    sort = single_data.get_userid_chart(userid)[4]
    style = single_data.get_userid_chart(userid)[5]
    suitable_population = single_data.get_userid_chart(userid)[6]
    suitable_scene = single_data.get_userid_chart(userid)[7]
    time_data = single_data.get_userid_chart(userid)[8]
    size_data = single_data.get_userid_chart(userid)[9]
    return render(request, 'user_portrait.html', {"userid":userid,"wordcloud_data": wordcloud_data,"creation_time":"".join(creation_time)
                                                  ,"product_color":"".join(product_color),"product_size":"".join(product_size)
                                                  ,"sort":"".join(sort), "style":"".join(style),"suitable_population":"".join(suitable_population)
                                                  ,"suitable_scene":"".join(suitable_scene),"time_data":time_data,"size_data":size_data})

def search(request):
    low_date = request.GET.get('low_date', '')
    high_date = request.GET.get('high_date', '')
    age_data=date_search_data.get_date(low_date,high_date)[0]
    sort = date_search_data.get_date(low_date, high_date)[1]
    man_data = date_search_data.get_date(low_date, high_date)[2]
    woman_data = date_search_data.get_date(low_date, high_date)[3]
    return render(request, 'graph_echarts.html',{"low_date":low_date,"high_date":high_date,"age_data":age_data,"sort":sort,"man_data":man_data,"woman_data":woman_data})

def detail_echarts(request):
    texture_data = detail_data_process.detail_data()[0]
    style_key = detail_data_process.detail_data()[1]
    style_value = detail_data_process.detail_data()[2]
    return render(request, 'detail_echarts.html',
                  {"texture_data":texture_data ,"style_key":style_key,"style_value":style_value})

def group_portrait(request):
    sortList = market_season_data.season_count()[1]
    sortwords = request.GET.get('sortInfo')
    sexwords = request.GET.get('sexInfo')
    wordclouds_data = group_data.group_process_data(sortwords,sexwords)[0]
    scatter = group_data.group_process_data(sortwords,sexwords)[1]
    period = group_data.group_process_data(sortwords,sexwords)[2]
    quarter = group_data.group_process_data(sortwords,sexwords)[3]
    group_date = group_data.group_process_data(sortwords,sexwords)[4]
    return render(request, 'group_portrait.html',{"sortwords":sortwords,"sexwords":sexwords,"sortList":sortList,"wordclouds_data":wordclouds_data
                                                ,"scatter":scatter,'period':period,'quarter':quarter,'quarter_name':list(group_date.keys())})

def profile(request):
    username=request.GET.get('username')
    # print(username)
    conn=connect()
    cursor = conn.cursor()
    check_sql = """
                   select * from web_userbaseinfo where username = %s;   
                """
    cursor.execute(check_sql,username)
    result_check = cursor.fetchall()
    conn.close()
    name=[]
    age=[]
    sex=[]
    address=[]
    tel=[]
    text=[]
    for i in result_check:
        name.append(i[2])
        age.append(i[3])
        sex.append(i[4])
        address.append(i[5])
        tel.append(i[6])
        text.append(i[7])
    return render(request,'profile.html',{"username":username,'age':" ".join('%s' %id for id in age),'sex':"".join(sex)
        ,'address':"".join(address),'tel':"".join(tel),'text':"".join(text),'name':"".join(name)})

def manage_user(request):
    data_dict = {}
    query = request.GET.get('query', "")
    if query:
        data_dict["username__contains"] = query
    queryset = models.user_info.objects.filter(**data_dict).order_by("user_id")
    page_object = pagination.Pagination(request,queryset)
    #分完页的数据
    page_queryset = page_object.page_queryset
    #页码
    page_string = page_object.html()
    return render(request,'user_list.html',{"query":query,"user_list":page_queryset,"page_string":page_string})

def add_user(request):
    if request.method == 'GET':
        return render(request,'user_add.html')
    username = request.POST.get('username')
    password = request.POST.get('password')
    re_password = request.POST.get('re_password')
    email = request.POST.get('email')
    role = request.POST.get('role')
    models.user_info.objects.create(username=username,password=password,email=email,mark=role)
    return redirect('/manage_user/')

def edit_user(request):
    user_id = request.GET.get('user_id')
    user_obj = models.user_info.objects.filter(user_id=user_id).first()
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        email = request.POST.get('email')
        models.user_info.objects.filter(user_id=user_id).update(username=username, password=password,email=email)
        return redirect('/manage_user/')
    return render(request, 'edit_user.html', locals())

def delete_user(request):
    user_id = request.GET.get('user_id')
    models.user_info.objects.filter(user_id=user_id).delete()
    return redirect('/manage_user/')

def delete_user_all(request):
    if request.method=="POST":
        values=request.POST.getlist('vals',[])
        for i in values:
            if i != '':
                user_obj = models.user_info.objects.get(user_id=i)
                user_obj.delete()
        return redirect('/manage_user/')

def set_user_all(request):
    if request.method=="POST":
        values=request.POST.getlist('vals',[])
        for i in values:
            if i != '':
                user_obj = models.user_info.objects.filter(user_id=i)
                user_obj.update(mark=1)
        return redirect('/manage_user/')

def set_manage(request):
    user_id = request.GET.get('user_id')
    models.user_info.objects.filter(user_id=user_id).update(mark=1)
    return redirect('/manage_user/')

def cancel_manage(request):
    user_id = request.GET.get('user_id')
    models.user_info.objects.filter(user_id=user_id).update(mark=0)
    return redirect('/manage_user/')

def show_user_info(request):
    data_dict = {}
    query = request.GET.get('query', "")
    if query:
        data_dict["username__contains"] = query
    queryset = models.userbaseinfo.objects.filter(**data_dict)
    page_object = pagination.Pagination(request, queryset)
    page_queryset = page_object.page_queryset
    page_string = page_object.html()
    return render(request, 'user_baseinfo.html', {"user_list": page_queryset, "page_string": page_string})

def edit_user_info(request):
    id = request.GET.get('id')
    user_obj = models.userbaseinfo.objects.filter(id=id).first()
    if request.method == 'POST':
        username = request.POST.get('username')
        name = request.POST.get('name')
        age = request.POST.get('age')
        sex = request.POST.get('sex')
        address = request.POST.get('address')
        tel = request.POST.get('tel')
        text = request.POST.get('text')
        print(username,name,age,sex,text)
        models.userbaseinfo.objects.filter(username=username).update(name=name, age=age,sex=sex,address=address,tel=tel,text=text)
        return redirect('/show_user_info/')
    return render(request, 'edit_userinfo.html', locals())

def feedback(request):
    data_dict = {}
    query = request.GET.get('search', "")
    if query:
        data_dict["email_context__contains"] = query
    queryset = models.mail.objects.filter(email_mark = "send",**data_dict).order_by("mail_id")
    page_object = pagination.Pagination(request, queryset)
    page_queryset = page_object.page_queryset
    page_string = page_object.html()
    return render(request, 'feedback.html', {"query":query,"feedback_data": page_queryset, "page_string": page_string})

def delete_mail_all(request):
    if request.method=="POST":
        values=request.POST.getlist('vals',[])
        for i in values:
            if i != '':
                mail_obj = models.mail.objects.get(mail_id=i)
                mail_obj.delete()
        return redirect('/manage_user/')

def mail_reply(request):
    id = request.GET.get('id')
    mail_obj = models.mail.objects.filter(mail_id=id).first()
    if request.method == 'POST':
        title = request.POST.get('title')
        receiver = request.POST.get('receiver')
        sender = request.POST.get('sender')
        email_context = request.POST.get('email_context')
        models.mail.objects.create(title=title, sender=sender, receiver=receiver, email_context=email_context,
                                   email_mark="receive")
    return render(request, 'mail_reply.html', locals())

def changeface(request):
    username= request.GET.get('username')
    print(username)
    if request.method == 'POST':
        dic_message = request.POST
        url = dic_message['img']
        username = dic_message['username']
        user_obj = models.userbaseinfo.objects.filter(username=username)
        if len(user_obj) >= 1:
            print("更新处理")
            models.userbaseinfo.objects.filter(username=username).update(image=url)
        else:
            print("插入处理")
            models.userbaseinfo.objects.create(username=username,image=url)
    conn=connect()
    cur=conn.cursor()
    cur.execute("""select * from web_userbaseinfo where username= %s""",username)
    image_url=cur.fetchall()
    image=[]
    if len(image_url)>=1:
        if image_url[0][8] == None:
            image = ''
        else:
            image.append(image_url[0][8])
            image = ''.join(image)
    else:
        image = ''
    return render(request, 'changeface.html',{"username":username,"url":image})