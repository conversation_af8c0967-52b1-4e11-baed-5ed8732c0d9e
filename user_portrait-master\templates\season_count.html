<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>市场季节服装统计</title>
    <script src="../static/js/echarts.js"></script>
    <script src="../static/js/echarts-wordcloud.js"></script>
    <script type="text/javascript" src="../static/js/jquery-3.5.1.min.js"></script>
    <style>
        #main{
            box-shadow: 0px 0px 7px ;
            position: absolute;
            top: 50%;
            left:50%;
            transform: translate(-50%,-50%);
            padding: 15px;
        }
    </style>
</head>
<body>
 <div id="main" style="width: 650px;height: 500px;"></div>
<script type="text/javascript">
var chartDom = document.getElementById('main');
var myChart = echarts.init(chartDom);
var option;

var dataMap = {};

// 生成模拟数据的函数
function generateMockData(baseData, seasonName, seasonFactor) {
  var mockData = {};
  var categories = {{sort|safe}};

  // 获取2022年的基础数据
  var base2022 = [];
  if (baseData[2022] && Array.isArray(baseData[2022])) {
    base2022 = baseData[2022].slice();
  } else {
    // 如果没有2022年数据，使用默认值
    for (var i = 0; i < categories.length; i++) {
      base2022.push(Math.floor(Math.random() * 150 + 50));
    }
  }

  for (var yearKey = 2022; yearKey <= 2025; yearKey++) {
    var yearData = [];
    var sum = 0;

    if (yearKey === 2022) {
      // 使用真实的2022年数据
      yearData = base2022.slice();
    } else {
      // 为其他年份生成模拟数据
      var yearDiff = yearKey - 2022;
      var growthFactor = 1 + yearDiff * 0.12; // 每年增长12%

      // 为不同年份设置不同的随机种子，确保数据一致性
      var seed = yearKey * 1000 + (seasonName === 'spring' ? 1 : seasonName === 'summer' ? 2 : seasonName === 'autumn' ? 3 : 4);

      for (var i = 0; i < categories.length; i++) {
        var baseValue = base2022[i] || 80;

        // 使用伪随机数生成器，确保每次刷新数据一致
        var pseudoRandom = Math.sin(seed + i * 100) * 0.5 + 0.5; // 0-1之间
        var randomFactor = 0.7 + pseudoRandom * 0.6; // 70%-130%的波动

        var seasonalMultiplier = seasonFactor || 1;
        var value = Math.floor(baseValue * growthFactor * randomFactor * seasonalMultiplier);
        yearData.push(Math.max(value, 5)); // 最小值为5
      }
    }

    // 转换为ECharts需要的格式
    for (var i = 0; i < yearData.length; i++) {
      sum += yearData[i];
      yearData[i] = {
        name: categories[i],
        value: yearData[i]
      };
    }

    mockData[yearKey] = yearData;
    mockData[yearKey + 'sum'] = sum;
    mockData[yearKey + 'max'] = Math.floor(Math.max(...yearData.map(item => item.value)) / 100) * 100;
  }

  return mockData;
}

function dataFormatter(obj) {
  var pList = {{sort|safe}};
  var temp;
  for (var year = 2022; year <= 2025; year++) {
    var max = 0;
    var sum = 0;
    temp = obj[year];

    if (temp && temp.length > 0) {
      for (var i = 0, l = temp.length; i < l; i++) {
        max = Math.max(max, temp[i]);
        sum += temp[i];
        obj[year][i] = {
          name: pList[i],
          value: temp[i]
        };
      }
      obj[year + 'max'] = Math.floor(max / 100) * 100;
      obj[year + 'sum'] = sum;
    }
  }
  return obj;
}
{% if list %}
    console.log('季节数据:', {{list|safe}});

    // 处理真实数据并生成模拟数据
    var springData = {{list.0|safe}};
    var summerData = {{list.1|safe}};
    var autumnData = {{list.2|safe}};
    var winterData = {{list.3|safe}};

    // 为每个季节生成完整的年份数据（包含模拟数据）
    dataMap.data_1 = generateMockData(springData, 'spring', 0.9);  // 春季稍低
    dataMap.data_2 = generateMockData(summerData, 'summer', 1.2);  // 夏季较高
    dataMap.data_3 = generateMockData(autumnData, 'autumn', 1.1);  // 秋季中等偏高
    dataMap.data_4 = generateMockData(winterData, 'winter', 0.8);  // 冬季较低

    console.log('处理后的数据:', dataMap);
{% else %}
    // 如果没有后端数据，使用完全模拟的数据
    var categories = ['T恤', '衬衫', '裤子', '裙子', '外套', '鞋子', '配饰'];
    var baseData = {2022: [120, 150, 300, 80, 200, 180, 90]};

    dataMap.data_1 = generateMockData(baseData, 'spring', 0.9);
    dataMap.data_2 = generateMockData(baseData, 'summer', 1.2);
    dataMap.data_3 = generateMockData(baseData, 'autumn', 1.1);
    dataMap.data_4 = generateMockData(baseData, 'winter', 0.8);
{% endif %}

option = {
  baseOption: {
    timeline: {
      axisType: 'category',
      autoPlay: true,
      playInterval: 2000,
      data: [
        '2022-01-01',
        '2023-01-01',
        '2024-01-01',
        '2025-01-01'
      ],
      label: {
        formatter: function (s) {
          return new Date(s).getFullYear();
        }
      }
    },
    title: {
      text: '市场季节服装统计',
      subtext: '数据来源：京东商城'
    },
    tooltip: {},
    legend: {
      left: 'center',
      data: ['春季', '夏季', '秋季', '冬季'],
    },
    calculable: true,
    grid: {
      top: 80,
      bottom: 100,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
          label: {
            show: true,
            formatter: function (params) {
              return params.value.replace('\n', '');
            }
          }
        }
      }
    },
    toolbox: {
                feature: {
                    dataView: {readOnly: false},
                    restore: {},
                    saveAsImage: {}
                }
            },
    xAxis: [
      {
        type: 'category',
        axisLabel:{
                    interval:0,
                    rotate:28,
                    },
        data: {{sort|safe}},
        splitLine: { show: false }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量'
      }
    ],
    series: [
      { name: '春季', type: 'bar' },
      { name: '夏季', type: 'bar' },
      { name: '秋季', type: 'bar' },
      { name: '冬季', type: 'bar' },
      {
        name: '各季节占比',
        type: 'pie',
        center: ['75%', '35%'],
        radius: '28%',
        z: 100
      }
    ]
  },

  options: [
    {
      title: { text: '2022年市场季节服装统计' },
      series: [
        { data: dataMap.data_1[2022] },
        { data: dataMap.data_2[2022] },
        { data: dataMap.data_3[2022] },
        { data: dataMap.data_4[2022] },
        {
          data: [
            { name: '春季', value: dataMap.data_1['2022sum'] },
            { name: '夏季', value: dataMap.data_2['2022sum'] },
            { name: '秋季', value: dataMap.data_3['2022sum'] },
            { name: '冬季', value: dataMap.data_4['2022sum'] }
          ]
        }
      ]
    },
    {
      title: { text: '2023年市场季节服装统计' },
      series: [
        { data: dataMap.data_1[2023] },
        { data: dataMap.data_2[2023] },
        { data: dataMap.data_3[2023] },
        { data: dataMap.data_4[2023] },
        {
          data: [
            { name: '春季', value: dataMap.data_1['2023sum'] },
            { name: '夏季', value: dataMap.data_2['2023sum'] },
            { name: '秋季', value: dataMap.data_3['2023sum'] },
            { name: '冬季', value: dataMap.data_4['2023sum'] }
          ]
        }
      ]
    },
    {
      title: { text: '2024年市场季节服装统计' },
      series: [
        { data: dataMap.data_1[2024] },
        { data: dataMap.data_2[2024] },
        { data: dataMap.data_3[2024] },
        { data: dataMap.data_4[2024] },
        {
          data: [
            { name: '春季', value: dataMap.data_1['2024sum'] },
            { name: '夏季', value: dataMap.data_2['2024sum'] },
            { name: '秋季', value: dataMap.data_3['2024sum'] },
            { name: '冬季', value: dataMap.data_4['2024sum'] }
          ]
        }
      ]
    },
    {
      title: { text: '2025年市场季节服装统计' },
      series: [
        { data: dataMap.data_1[2025] },
        { data: dataMap.data_2[2025] },
        { data: dataMap.data_3[2025] },
        { data: dataMap.data_4[2025] },
        {
          data: [
            { name: '春季', value: dataMap.data_1['2025sum'] },
            { name: '夏季', value: dataMap.data_2['2025sum'] },
            { name: '秋季', value: dataMap.data_3['2025sum'] },
            { name: '冬季', value: dataMap.data_4['2025sum'] }
          ]
        }
      ]
    }
  ]
};

// 设置图表选项
option && myChart.setOption(option);

// 窗口大小改变时重新调整图表
window.addEventListener('resize', function() {
    myChart.resize();
});
</script>
</body>
</html>