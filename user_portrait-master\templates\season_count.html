<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>市场季节服装统计</title>
    <script src="../static/js/echarts.js"></script>
    <script src="../static/js/echarts-wordcloud.js"></script>
    <script type="text/javascript" src="../static/js/jquery-3.5.1.min.js"></script>
    <style>
        #main{
            box-shadow: 0px 0px 7px ;
            position: absolute;
            top: 50%;
            left:50%;
            transform: translate(-50%,-50%);
            padding: 15px;
        }
    </style>
</head>
<body>
 <div id="main" style="width: 650px;height: 500px;"></div>
<script type="text/javascript">
var chartDom = document.getElementById('main');
var myChart = echarts.init(chartDom);
var option;

var dataMap = {};
function dataFormatter(obj) {
  // prettier-ignore
  var pList = {{sort|safe}}
  var temp;
  for (var year = 2022; year <= 2025; year++) {
    var max = 0;
    var sum = 0;
    temp = obj[year];
    // console.log('asdasdasd',temp)
    for (var i = 0, l = temp.length; i < l; i++) {
      max = Math.max(max, temp[i]);
      sum += temp[i];
      obj[year][i] = {
        name: pList[i],
        value: temp[i]
      };
    }
    obj[year + 'max'] = Math.floor(max / 100) * 100;
    obj[year + 'sum'] = sum;
  }
  return obj;
}
{% if list %}
    console.log('季节数据:', {{list|safe}});
    dataMap.data_1 = dataFormatter({{list.0|safe}});
    dataMap.data_2 = dataFormatter({{list.1|safe}});
    dataMap.data_3 = dataFormatter({{list.2|safe}});
    dataMap.data_4 = dataFormatter({{list.3|safe}});
{% endif %}

option = {
  baseOption: {
    timeline: {
      axisType: 'category',
      autoPlay: true,
      playInterval: 2000,
      data: [
        '2022-01-01',
        '2023-01-01',
        '2024-01-01',
        '2025-01-01'
      ],
      label: {
        formatter: function (s) {
          return new Date(s).getFullYear();
        }
      }
    },
    title: {
      text: '市场季节服装统计',
      subtext: '数据来源：京东商城'
    },
    tooltip: {},
    legend: {
      left: 'center',
      data: ['春季', '夏季', '秋季', '冬季'],
    },
    calculable: true,
    grid: {
      top: 80,
      bottom: 100,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
          label: {
            show: true,
            formatter: function (params) {
              return params.value.replace('\n', '');
            }
          }
        }
      }
    },
    toolbox: {
                feature: {
                    dataView: {readOnly: false},
                    restore: {},
                    saveAsImage: {}
                }
            },
    xAxis: [
      {
        type: 'category',
        axisLabel:{
                    interval:0,
                    rotate:28,
                    },
        data: {{sort|safe}},
        splitLine: { show: false }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量'
      }
    ],
    series: [
      { name: '春季', type: 'bar' },
      { name: '夏季', type: 'bar' },
      { name: '秋季', type: 'bar' },
      { name: '冬季', type: 'bar' },
      {
        name: '各季节占比',
        type: 'pie',
        center: ['75%', '35%'],
        radius: '28%',
        z: 100
      }
    ]
  },

  options: [
    {
      title: { text: '2022年市场季节服装统计' },
      series: [
        { data: dataMap.data_1[2022] },
        { data: dataMap.data_2[2022] },
        { data: dataMap.data_3[2022] },
        { data: dataMap.data_4[2022] },
        {
          data: [
            { name: '春季', value: dataMap.data_1['2022sum'] },
            { name: '夏季', value: dataMap.data_2['2022sum'] },
            { name: '秋季', value: dataMap.data_3['2022sum'] },
            { name: '冬季', value: dataMap.data_4['2022sum'] }
          ]
        }
      ]
    },
    {
      title: { text: '2023年市场季节服装统计' },
      series: [
        { data: dataMap.data_1[2023] },
        { data: dataMap.data_2[2023] },
        { data: dataMap.data_3[2023] },
        { data: dataMap.data_4[2023] },
        {
          data: [
            { name: '春季', value: dataMap.data_1['2023sum'] },
            { name: '夏季', value: dataMap.data_2['2023sum'] },
            { name: '秋季', value: dataMap.data_3['2023sum'] },
            { name: '冬季', value: dataMap.data_4['2023sum'] }
          ]
        }
      ]
    },
    {
      title: { text: '2024年市场季节服装统计' },
      series: [
        { data: dataMap.data_1[2024] },
        { data: dataMap.data_2[2024] },
        { data: dataMap.data_3[2024] },
        { data: dataMap.data_4[2024] },
        {
          data: [
            { name: '春季', value: dataMap.data_1['2024sum'] },
            { name: '夏季', value: dataMap.data_2['2024sum'] },
            { name: '秋季', value: dataMap.data_3['2024sum'] },
            { name: '冬季', value: dataMap.data_4['2024sum'] }
          ]
        }
      ]
    },
    {
      title: { text: '2025年市场季节服装统计' },
      series: [
        { data: dataMap.data_1[2025] },
        { data: dataMap.data_2[2025] },
        { data: dataMap.data_3[2025] },
        { data: dataMap.data_4[2025] },
        {
          data: [
            { name: '春季', value: dataMap.data_1['2025sum'] },
            { name: '夏季', value: dataMap.data_2['2025sum'] },
            { name: '秋季', value: dataMap.data_3['2025sum'] },
            { name: '冬季', value: dataMap.data_4['2025sum'] }
          ]
        }
      ]
    }
  ]
};
option && myChart.setOption(option);
</script>
</body>
</html>
</body>
</html>