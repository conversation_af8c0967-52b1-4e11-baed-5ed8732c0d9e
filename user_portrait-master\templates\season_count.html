<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>群体画像</title>
    <script src="../static/js/echarts.js"></script>
    <script src="../static/js/echarts-wordcloud.js"></script>
    <script type="text/javascript" src="../static/js/jquery-3.5.1.min.js"></script>
    <style>
        #main{
            box-shadow: 0px 0px 7px ;
            position: absolute;
            top: 50%;
            left:50%;
            transform: translate(-50%,-50%);
            padding: 15px;
        }
    </style>
</head>
<body>
 <div id="main" style="width: 650px;height: 500px;"></div>
 <div id="bar" style="width: 600px;height: 400px;"></div>
<script type="text/javascript">
var chartDom = document.getElementById('main');
var myChart = echarts.init(chartDom);
var option;

var dataMap = {};
function dataFormatter(obj) {
  // prettier-ignore
  var pList = {{sort|safe}}
  var temp;
  for (var year = 2022; year <= 2025; year++) {
    var max = 0;
    var sum = 0;
    temp = obj[year];
    // console.log('asdasdasd',temp)
    for (var i = 0, l = temp.length; i < l; i++) {
      max = Math.max(max, temp[i]);
      sum += temp[i];
      obj[year][i] = {
        name: pList[i],
        value: temp[i]
      };
    }
    obj[year + 'max'] = Math.floor(max / 100) * 100;
    obj[year + 'sum'] = sum;
  }
  return obj;
}
{% if list %}
    console.log('asdasdsadsadsad',dataFormatter({{list.3}}))
    dataMap.data_1 = dataFormatter({{list.0}});
    dataMap.data_2 = dataFormatter({{list.1}});
    dataMap.data_3 = dataFormatter({{list.2}});
    dataMap.data_4 =dataFormatter({{list.3}});

{% endif %}

option = {
  baseOption: {
    timeline: {
      axisType: 'category',
      autoPlay: true,
      playInterval: 1000,
      data: [
        '2022-01-01',
        '2025-01-01',
         {
          value: '2022-01-01',
          tooltip: {
            formatter: '{b} 数据较完整'
          },
          symbol: 'diamond',
          symbolSize: 16
        },
        '2022-01-01',
      ],
      label: {
        formatter: function (s) {
          return new Date(s).getFullYear();
        }
      }
    },
    title: {
      subtext: '数据来京东商城'
    },
    tooltip: {},
    legend: {
      left: 'center',
      data: ['春季', '夏季', '秋季', '冬季'],
    },
    calculable: true,
    grid: {
      top: 80,
      bottom: 100,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
          label: {
            show: true,
            formatter: function (params) {
              return params.value.replace('\n', '');
            }
          }
        }
      }
    },
    toolbox: {
                feature: {
                    dataView: {readOnly: false},
                    restore: {},
                    saveAsImage: {}
                }
            },
    xAxis: [
      {
        type: 'category',
        axisLabel:{
                    interval:0,
                    rotate:28,
                    },
        data: {{sort|safe}},
        splitLine: { show: false }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '数量'
      }
    ],
    series: [
      { name: '春季', type: 'bar' },
      { name: '夏季', type: 'bar' },
      { name: '秋季', type: 'bar' },
      { name: '冬季', type: 'bar' },
      {
        name: '各季节占比',
        type: 'pie',
        center: ['75%', '35%'],
        radius: '28%',
        z: 100
      }
    ]
  },

  options: [
    {
      title: { text: '2022年' },
      series: [
        { data: dataMap.data_1['2019'] },
        { data: dataMap.data_2['2019'] },
        { data: dataMap.data_3['2019'] },
        { data: dataMap.data_3['2019'] },
        {
          data: [
            { name: '春季', value: dataMap.data_1['2019sum'] },
            { name: '夏季', value: dataMap.data_2['2019sum'] },
            { name: '秋季', value: dataMap.data_3['2019sum'] },
            { name: '冬季', value: dataMap.data_4['2019sum'] }
          ]
        }
      ]
    },
    {
      title: { text: '2023年' },
      series: [
        { data: dataMap.data_1['2020'] },
        { data: dataMap.data_2['2020'] },
        { data: dataMap.data_3['2020'] },
        { data: dataMap.data_3['2020'] },
        {
          data: [
            { name: '春季', value: dataMap.data_1['2020sum'] },
            { name: '夏季', value: dataMap.data_2['2020sum'] },
            { name: '秋季', value: dataMap.data_3['2020sum'] },
            { name: '冬季', value: dataMap.data_4['2020sum'] }
          ]
        }
      ]
    },
    {
      title: { text: '2024年' },
      series: [
         { data: dataMap.data_1['2021'] },
        { data: dataMap.data_2['2021'] },
        { data: dataMap.data_3['2021'] },
        { data: dataMap.data_3['2021'] },
        {
          data: [
            { name: '春季', value: dataMap.data_1['2021sum'] },
            { name: '夏季', value: dataMap.data_2['2021sum'] },
            { name: '秋季', value: dataMap.data_3['2021sum'] },
            { name: '冬季', value: dataMap.data_4['2021sum'] }
          ]
        }
      ]
    },
    {
      title: { text: '2025年' },
      series: [
        { data: dataMap.data_1['2022'] },
        { data: dataMap.data_2['2022'] },
        { data: dataMap.data_3['2022'] },
        { data: dataMap.data_3['2022'] },
        {
          data: [
            { name: '春季', value: dataMap.data_1['2022sum'] },
            { name: '夏季', value: dataMap.data_2['2022sum'] },
            { name: '秋季', value: dataMap.data_3['2022sum'] },
            { name: '冬季', value: dataMap.data_4['2022sum'] }
          ]
        }
      ]
    }
  ]
};
option && myChart.setOption(option);
</script>
</body>
</html>
</body>
</html>