<!DOCTYPE html>
<html lang="zh">
<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>welcome</title>
<!--	<link href="http://cdn.bootcss.com/bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">-->
<!--	<link href="http://cdn.bootcss.com/font-awesome/4.6.3/css/font-awesome.min.css" rel="stylesheet">-->
	<link rel="stylesheet" type="text/css" href="../static/css/htmleaf-demo.css">
	<link rel="stylesheet" type="text/css" href="../static/css/bootstrap.min.css">
	<link rel="stylesheet" type="text/css" href="../static/css/font-awesome.min.css">
	<style class="cp-pen-styles">*,
		*:before,
		*:after {
		  box-sizing: border-box;
		}

		body {
		  margin: 0;
		  font-family: "Poppins", sans-serif;
		  font-size: 62.5%;
		}

		::-webkit-scrollbar {
		  width: 0;
		}

		header {
		  position: relative;
		  height: 100vh;
		  overflow: hidden;
		  display: flex;
		  justify-content: center;
		  align-items: center;
		  background-color: #c6d9d3;
		}
		header .overlay {
		  position: absolute;
		  top: 0;
		  left: 0;
		  width: 100%;
		  height: 100%;
		}
		header .overlay h1 {
		  position: absolute;
		  top: 50%;
		  left: 50%;
		  transform: translate(-50%, -50%);
		  z-index: 3;
		  margin: 0;
<!--		  text-transform: uppercase;-->
		  text-align: center;
		  font-size: 10rem;
		  text-shadow: 25px -15px rgba(25, 42, 46, 0.04);
		  color: #142124;
		  mix-blend-mode: overlay;
		}
		header .overlay h1 span {
		  margin-top: -2em;
		  padding-top: 0.5em;
		  display: block;
		  letter-spacing: 5px;
		  font-size: 1.25rem;
		}
		header .parallax {
		  position: relative;
		  width: 100%;
		  height: 100%;
		}
		header .parallax div {
		  position: absolute;
		  bottom: 0;
		  left: 0;
		  width: 100%;
		  height: 100%;
		  background-position: 0 50%;
		  background-repeat: no-repeat;
		  background-size: 100%;
		}
		header .parallax .one {
		  background-image: url("../static/svg/mountains-1.svg");
		  z-index: 4;
		}
		header .parallax .two {
		  background-image: url("../static/svg/mountains-2.svg");
		  z-index: 3;
		}
		header .parallax .three {
		  background-image: url("../static/svg/mountains-3.svg");
		  z-index: 2;
		}
		header .parallax .four {
		  background-image: url("../static/svg/mountains-4.svg");
		  z-index: 1;
		}
		header .parallax .five {
		  background-image: url("../static/svg/mountains-5.svg");
		  z-index: 0;
		}
		</style>
	<style type="text/css">
		.demo{
			background: #fff;
		}
		.main-timeline{ overflow: auto; }
		.main-timeline .timeline{
		    padding: 0;
		    text-align: center;
		    overflow: hidden;
		}
		.main-timeline .timeline-icon{
		    padding-bottom: 20px;
		    margin-bottom: 30px;
		    font-size: 65px;
		    color: #727cb6;
		    border-bottom: 3px solid #ccc;
		    position: relative;
		}
		.main-timeline .timeline-icon:before{
		    content: "";
		    width: 16px;
		    height: 16px;
		    border-radius: 50%;
		    background: #727cb6;
		    margin: 0 auto;
		    position: absolute;
		    bottom: -9.5px;
		    left: 0;
		    right: 0;
		}
		.main-timeline .year{
		    display: block;
		    width: 120%;
		    height: 100px;
		    line-height: 100px;
		    border-radius: 50%;
		    background: #727cb6;
		    font-size: 30px;
		    color: #fff;
		    margin-bottom: 30px;
		    z-index: 1;
		    position: relative;
			margin-left: -10px;
		}
		.main-timeline .year:before{
		    content: "";
		    display: block;
		    width: 70px;
		    height: 80%;
		    background: #fff;
		    border-radius: 50%;
		    position: absolute;
		    top: 10px;
		    left: -37px;
		}
		.main-timeline .timeline-content{ padding: 0 10px; }
		.main-timeline .post{
		    font-size: 20px;
		    color: #727cb6;
		    margin: 0 0 20px 20px;
		}
		.main-timeline .description{
			font-size: 12px;
			color: #555;
			text-align: left;
			margin-left: 15px;
			text-indent: 2em;
		}
		.main-timeline .timeline:nth-child(2n) .timeline-icon,
		.main-timeline .timeline:nth-child(2n) .post{
		    color: #008b8b;
		}
		.main-timeline .timeline:nth-child(2n) .year,
		.main-timeline .timeline:nth-child(2n) .timeline-icon:before{
		    background: #008b8b;
		}
		.main-timeline .timeline:nth-child(3n) .timeline-icon,
		.main-timeline .timeline:nth-child(3n) .post{
		    color: #e77e21;
		}
		.main-timeline .timeline:nth-child(3n) .year,
		.main-timeline .timeline:nth-child(3n) .timeline-icon:before{
		    background: #e77e21;
		}
		.main-timeline .timeline:nth-child(4n) .timeline-icon,
		.main-timeline .timeline:nth-child(4n) .post{
		    color: #3498db;
		}
		.main-timeline .timeline:nth-child(4n) .year,
		.main-timeline .timeline:nth-child(4n) .timeline-icon:before{
		    background: #3498db;
		}
		.main-timeline .timeline:nth-child(5n) .timeline-icon,
		.main-timeline .timeline:nth-child(5n) .post{
		    color: #cca872;
		}
		.main-timeline .timeline:nth-child(5n) .year,
		.main-timeline .timeline:nth-child(5n) .timeline-icon:before{
		    background: #cca872;
		}
		.main-timeline .timeline:nth-child(6n) .timeline-icon,
		.main-timeline .timeline:nth-child(6n) .post{
		    color: #ed687c;
		}
		.main-timeline .timeline:nth-child(6n) .year,
		.main-timeline .timeline:nth-child(6n) .timeline-icon:before{
		    background: #ed687c;
		}
	</style>
	<script>
	// 在页面加载前阻止Layer的自动CSS加载
	window.layui_config = {
	    base: '/static/layui/',
	    version: false,
	    debug: false
	};

	// 禁用Layer的自动CSS加载
	if (typeof layui !== 'undefined') {
	    layui.config({
	        version: false
	    });
	}
	</script>

	<!-- 然后是正常的CDN引用 -->
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/layui@2.8.18/dist/css/layui.css">
	<script src="https://cdn.jsdelivr.net/npm/layui@2.8.18/dist/layui.js"></script>
</head>
<body>
	<header>
	  <div class="overlay">
		<div class="parallax">
		  <h1>User Portrait</h1>
		  <div class="one"></div>
		  <div class="two"></div>
		  <div class="three"></div>
		  <div class="four"></div>
		  <div class="five"></div>
		</div>
	  </div>
	</header>
	<div class="htmleaf-container">
		<div class="demo">
		        <div class="container dv">
		            <div class="row">
		                <div class="main-timeline">
		                    <div class="col-md-2 col-sm-6 timeline" id="dv1" style="visibility:hidden">
		                        <div class="timeline-icon"><i class="fa fa-cart-plus"></i></div>
		                        <span class="year">线下购物</span>
		                        <div class="timeline-content">
		                            <div class="post">Shop Offline</div>
		                            <p class="description">
									从一家便利店说起……
									便利店的店主往往会给到访的顾客打上不同的标签。比如
									经常到店购买的顾客，店主将其判定为熟客，如果客人没有带足够的现金，那么就容许进行赊账；
									老板根据顾客在店里的消费行为进行判断，当顾客购买价格比较高的啤酒，
									可以推测这个顾客的生活品质不错，有了新口味的饮品时，也会考虑给顾客进行安利……
		                            </p>
		                        </div>
		                    </div>

		                    <div class="col-md-2 col-sm-6 timeline" id="dv2" style="visibility:hidden">
		                        <div class="timeline-icon"><i class="fa fa-globe"></i></div>
		                        <span class="year">互联网发展</span>
		                        <div class="timeline-content">
		                            <div class="post">Web Begining</div>
		                            <p class="description">
										再后来，网络购物兴起了，商品的浏览、交易都发生在了线上。
										从线下的察言观色变成线上的用户行为洞察。
										所以，第一批的画像系统的生产者，并不是平台方，而是提供服务的群体本身。
										他们猜测顾客的喜好以便更快地销售商品。
		                            </p>
		                        </div>
		                    </div>

		                    <div class="col-md-2 col-sm-6 timeline" id="dv3" style="visibility:hidden">
		                        <div class="timeline-icon"><i class="fa fa-briefcase"></i></div>
		                        <span class="year">大数据时代</span>
		                        <div class="timeline-content">
		                            <div class="post">Web Developer</div>
		                            <p class="description">
										平台方为帮助店家更好运营网店：
										一、构建全局的用户画像：平台掌握大量的用户消费及行为数据，通过生成标签建立用户画像；
										二、引入了推荐系统，为不同人群推荐合适的商品或内容，快速找到心意的商品；
										三、平台方推出了运营工具，提供了一些广告推广服务，引入了在线客服让商家为顾客提供更优质的服务。
		                            </p>
		                        </div>
		                    </div>

		                    <div class="col-md-2 col-sm-6 timeline" id="dv4" style="visibility:hidden">
		                        <div class="timeline-icon"><i class="fa fa-mobile"></i></div>
		                        <span class="year">用户画像1.0</span>
		                        <div class="timeline-content">
		                            <div class="post">User Portrait 1.0</div>
		                            <p class="description">
										逐渐地人们意识到了用户画像的重要性，从而在产品中不断进行新的尝试，画像系统也随之诞生了
										第一代画像系统，可简单的定义为：标签时代。
										画像系统的 1.0 版本，重要的功能就是标签的数据内容，而且还很多是第三方的标签数据。
										如今，最开始做画像的人群，基本都在做广告投放相关的业务。
		                            </p>
		                        </div>
		                    </div>

		                    <div class="col-md-2 col-sm-6 timeline" id="dv5" style="visibility:hidden">
		                        <div class="timeline-icon"><i class="fa fa-cube"></i></div>
		                        <span class="year">用户画像2.0</span>
		                        <div class="timeline-content">
		                            <div class="post">User Portrait 2.0</div>
		                            <p class="description">
										随着流量逐渐达到了天花板，用户运营的价值凸显了出来。
										第二代的画像系统，我们可以简单的定义为：洞察时代。
										企业需要尽可能地收集用户在产品中的使用痕迹，根据自己累积的业务经验和知识，来刻画出用户的画像。
										第二代的画像系统，需要业务人员倾注更多的业务理解和智慧在其中，才可以做好运营和增长
										，发挥画像系统的威力。
		                            </p>
		                        </div>
		                    </div>
							<div class="col-md-2 col-sm-6 timeline"  id="dv6" style="visibility:hidden">
		                        <div class="timeline-icon"><i class="fa fa-diamond"></i></div>
		                        <span class="year">项目</span>
		                        <div class="timeline-content">
		                            <div class="post">My Project</div>
		                            <p class="description">
		                                本系统根据用户角色的不同，设置不同的实现功能模块。
										普通用户实现用户画像、统计分析、个人资料、信息反馈、配饰搭配、款式推荐等功能模块；
										管理员可对用户账号、用户基本信息进行管理以及对用户的反馈信息进行回复。
		                            </p>
		                        </div>
		                    </div>
		                </div>
		            </div>
		        </div>
		    </div>
	</div>
	<script>
	var n = 0;
	function showDiv() {
		n++;
		if (n <= 100) {
			var element = document.getElementById("dv" + n);
			if (element) {
				element.style.visibility = "visible";
			}
			setTimeout(showDiv, 1000);
		}
	}
	window.onload = showDiv;
	</script>
	<script src="../static/js/jquery-2.1.1.min.js"></script>
	<script>
		$(window).scroll(function () {
		  const
		  a = $(this).scrollTop(),
		  b = 800;
		  $("h1").css({
		    backgroundPosition: "center " + a / 2 + "px" });

		  $(".parallax").css({
		    top: a / 1.6 + "px",
		    opacity: 1 - a / b });

		});

		// parallax scrolling

		document.addEventListener("scroll", () => {
		  const
		  top = window.pageYOffset,
		  one = document.querySelector(".one"),
		  two = document.querySelector(".two"),
		  three = document.querySelector(".three"),
		  four = document.querySelector(".four"),
		  five = document.querySelector(".five");

		  one.style.bottom = -(top * 0.1) + "px";
		  two.style.bottom = -(top * 0.2) + "px";
		  three.style.bottom = -(top * 0.3) + "px";
		  four.style.bottom = -(top * 0.4) + "px";
		  five.style.bottom = -(top * 0.5) + "px";
		});

	</script>
</body>
</html>

