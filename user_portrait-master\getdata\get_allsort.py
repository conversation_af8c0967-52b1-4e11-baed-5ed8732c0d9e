from lxml import etree
import requests
from pymysql import connect

def get_sku_sort_all(url):
    mall_name = "京东商城"
    html = requests.get(url).text
    etree_html = etree.HTML(html)
    etree_html_col = etree_html.xpath('/html/body/div[5]/div[2]/div[1]/div[2]/*')
    etree_html_col_m = etree_html_col[0].xpath('/html/body/div[5]/div[2]/div[1]/div[2]/div[1]/*')
    # print(len(etree_html_col_m))  #11(0.1.2)
    etree_html_col_mc = etree_html_col_m[0].xpath('/html/body/div[5]/div[2]/div[1]/div[2]/div[1]/div[9]/div[2]/div[3]/*')
    # 获取大一类
    sku_sort_one = etree_html_col_m[0].xpath('/html/body/div[5]/div[2]/div[1]/div[2]/div[1]/div[9]/div[1]/h2/span/text()')
    for mc_i in range(2):
        etree_html_col_mc_dd = etree_html_col_mc[mc_i].xpath('/html/body/div[5]/div[2]/div[1]/div[2]/div[1]/div[9]/div[2]/div/dl[{}]/dd/*'.format(mc_i+1))
        sku_sort_two = etree_html_col_mc[mc_i].xpath('/html/body/div[5]/div[2]/div[1]/div[2]/div[1]/div[9]/div[2]/div/dl[{}]/dt/a/text()'.format(mc_i+1))
        for dd_i in range(len(etree_html_col_mc_dd)):
            # 获取大三类
            sku_sort_three = etree_html_col_mc_dd[dd_i].xpath('/html/body/div[5]/div[2]/div[1]/div[2]/div[1]/div[9]/div[2]/div[3]/dl[{}]/dd/a[{}]/text()'.format(mc_i+1, dd_i+1))
            # 获取大三类URL
            sku_sort_three_url = etree_html_col_mc_dd[dd_i].xpath('/html/body/div[5]/div[2]/div[1]/div[2]/div[1]/div[9]/div[2]/div[3]/dl[{}]/dd/a[{}]/@href'.format(mc_i+1, dd_i+1))
            val = (mall_name, sku_sort_one[0][:2], sku_sort_two[0][:1]+sku_sort_three[0], 'https:' + sku_sort_three_url[0].replace('jd', 'jd').replace(',', '%2C'))
            print(val)
            conn = connect(host="localhost",
                           port=3306,
                           user="root",
                           password="root",
                           database="user_portrait",
                           charset="utf8mb4", )
            # 获取cursor对象
            cur = conn.cursor()
            # 执行sql语句
            query = 'insert into sort_url(mall_name,sku_sort,sex,sku_sort_detail,sku_full_sort,sku_sort_url) values(%s,%s, %s, %s, %s, %s)'
            cur.execute(query, (mall_name, sku_sort_one[0][:2], sku_sort_two[0][:1],sku_sort_three[0],sku_sort_two[0][:1]+sku_sort_three[0], 'https:' + sku_sort_three_url[0].replace('jd', 'jd').replace(',', '%2C')+'&page='))
            # 提交之前的操作，如果之前已经执行多次的execute，那么就都进行提交
            conn.commit()
            # 关闭cursor对象
            cur.close()
            # 关闭connection对象
            conn.close()


if __name__ == '__main__':
    url = 'https://www.jd.com/allSort.aspx'     # 获取某某商城的分类数据
    get_sku_sort_all(url)