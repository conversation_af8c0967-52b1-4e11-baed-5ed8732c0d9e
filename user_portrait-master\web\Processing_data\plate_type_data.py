from pymysql import Connect
import pandas as pd
from ..MysqlConnect import connect_get

def plate_data_processing():
    count_sql = """
                   select plate_type,year(b.creation_time),thickness,texture,1 from detail a,dev_content b 
                   where a.pid=b.pid order by year(b.creation_time);
                 """
    result_count = connect_get(count_sql)
    df_count = pd.DataFrame(list(result_count),columns=['plate_type','year','thickness','texture','count'])
    other=['other']
    standard=["standard"]
    lax=['lax']
    tight=["tight"]
    A_type=["A_type"]
    allyear=["market_time","2023","2024","2025"]
    for year in df_count['year'].unique():
        for type_i in df_count['plate_type'].unique():
            result = df_count[(df_count.year == year) & (df_count.plate_type == type_i)]
            if type_i == "其他":
                other.append(result.shape[0])
            elif type_i == "标准型":
                standard.append(result.shape[0])
            elif type_i == "宽松型":
                lax.append(result.shape[0])
            elif type_i == "修身型":
                tight.append(result.shape[0])
            elif type_i == "A字型":
                A_type.append(result.shape[0])
    plate_type_list=[allyear,other,standard,lax,tight,A_type]
    print(plate_type_list)
    thick_count = df_count.groupby(['thickness']).count()['count']
    thickness=[]
    thick_key = list(thick_count.keys())
    thick_value = list(thick_count)
    for i in range(len(thick_key)):
        thickness.append({"value":thick_value[i],"name":thick_key[i]})
    return plate_type_list,thickness
