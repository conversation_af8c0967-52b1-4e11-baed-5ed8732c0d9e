import re

from jieba.analyse import extract_tags
from pymysql import Connect
import pandas as pd
import numpy as np
from collections import Counter
# 方法四：余弦相似度
def cos_sim(str1, str2):  # str1，str2是分词后的标签列表
    co_str1 = (Counter(str1))
    co_str2 = (Counter(str2))
    p_str1 = []
    p_str2 = []
    for temp in set(str1 + str2):
        p_str1.append(co_str1[temp])
        p_str2.append(co_str2[temp])
    p_str1 = np.array(p_str1)
    p_str2 = np.array(p_str2)
    return p_str1.dot(p_str2) / (np.sqrt(p_str1.dot(p_str1)) * np.sqrt(p_str2.dot(p_str2)))

conn = Connect(host="127.0.0.1", user="root", password="root", database="shujuku", port=3306)
cursor = conn.cursor()
count_sql = """
               select a.texture,plate_type,thickness,style,1 from detail a,dev_content b where a.pid=b.pid;
             """
cursor.execute(count_sql)
result_count = cursor.fetchall()
conn.close()
df_count = pd.DataFrame(list(result_count),columns=['texture','plate_type','thickness','style','count'])
# 数据库中sql处理detail表中的plate_type类型数据
plate_count=df_count.groupby(['plate_type']).count()['count']
plate_key=list(plate_count.keys())
plate_value=list(plate_count)
print("plate",plate_key,plate_value)
# 数据库中sql处理detail表中的thickness类型数据
thick_count=df_count.groupby(['thickness']).count()['count']
thick_key=list(thick_count.keys())
thick_value=list(thick_count)
print("thick",thick_key,thick_value)
words= {}
style_words=[]
#texture类型数据
max_texture=[]
texture_count={'棉':0,'涤纶':0,'化学纤维':0,'桑蚕丝':0,'动物材质(羊、牛、狐狸)':0,'其他':0}
# for text_word in result_count:
#     print(text_word)
#     for texture_key in list(texture_count.keys()):
#         word_similar=cos_sim(texture_key, text_word[0])
#         print(texture_key,text_word[0],word_similar)

texture_list=list(texture_count.keys())
for text_word in result_count:
    word = text_word[3]
    pattern = re.compile(r'[^\u4e00-\u9fa5]')
    chinese = re.sub(pattern, " ", word)
    chinese_words = chinese.split(' ')
    style_words.append(chinese_words)
    word_similar_0 = cos_sim(texture_list[0], text_word[0])
    word_similar_1 = cos_sim(texture_list[1], text_word[0])
    word_similar_2 = cos_sim(texture_list[2], text_word[0])
    word_similar_3 = cos_sim(texture_list[3], text_word[0])
    word_similar_4 = cos_sim(texture_list[4], text_word[0])
    word_similar_5 = cos_sim(texture_list[5], text_word[0])
    compare_list = [word_similar_0,word_similar_1,word_similar_2,word_similar_3,word_similar_4,word_similar_5]
    # print(compare_list)
    compare_index=compare_list.index(max(compare_list))
    texture_key=texture_list[compare_index]
    texture_count[texture_key]+=1
print("texture",texture_count)
for i in style_words:
    for j in i:
        if (len(j)) > 1:
            if j not in words:
                words[j] = 1
            else:
                words[j] += 1
print("style",words)










