<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta name="renderer" content="webkit" />
		<title>用户画像系统</title>
		<link href="../static/css/bootstrap.min.css?v=3.3.7" rel="stylesheet" />
		<link href="../static/css/font-awesome.min.css?v=4.4.0" rel="stylesheet" />
		<link href="../static/css/animate.css" rel="stylesheet" />
		<link href="../static/css/style.css?v=4.1.0" rel="stylesheet" />
		<link href="../static/css/jquery.contextMenu.min.css" rel="stylesheet"/>
		<script type="text/javascript" src="../static/js/jquery-3.5.1.min.js"></script>
	</head>

	<body class="fixed-sidebar full-height-layout gray-bg" style="overflow: hidden;">
		<div id="wrapper">
			<nav class="navbar-default navbar-static-side" role="navigation">
				<div class="nav-close"><i class="fa fa-times-circle"></i></div>
				<div class="sidebar-collapse">
					<ul class="nav" id="side-menu">
						<li class="nav-header">
							<div class="dropdown profile-element">
								<span>
									{% if url != ""%}
										<img alt="image" class="img-circle" src="{{url}}" style="width:64px;height:64px" />
									{% else %}
										<img alt="image" class="img-circle" src="../static/images/profile_small.jpg" style="width:64px;height:64px"/>
									{% endif %}
								</span>
								<a data-toggle="dropdown" class="dropdown-toggle" href="#">
									<span class="clear">
										<span class="text-muted text-xs block m-t-xs" style="font-size: 15px;">{{name}}<b class="caret"></b></span>
									</span>
								</a>
								<ul class="dropdown-menu animated fadeInRight m-t-xs">
									<li><a class="J_menuItem" href="/changeface/?username={{name}}">更换头像</a></li>
									<li><a class="J_menuItem" href="/profile/?username={{name}}">个人资料</a></li>
									<li><a class="J_menuItem" href="/mail_data/?username={{name}}">联系我们</a></li>
									<li class="divider"></li>
									<li><a href="/login">安全退出</a></li>
								</ul>
							</div>
						</li>
						<li>
							<a href="#">
								<i class="fa fa-home"></i>
								<span class="nav-label">主页</span>
								<span class="fa arrow"></span>
							</a>
							<ul class="nav nav-second-level">
								<li>
									<a class="J_menuItem" href="/welcome" data-index="0">首页</a>
								</li>
							</ul>
						</li>
						<li>
							<a href="#"><i class="fa fa-picture-o"></i> <span class="nav-label">用户画像</span><span class="fa arrow"></span></a>
							<ul class="nav nav-second-level">
								<li><a class="J_menuItem" href="/single_view">个人画像</a></li>
								<li><a class="J_menuItem" href="/group_portrait">群体画像</a></li>
							</ul>
						</li>
						<li>
							<a href="#">
								<i class="fa fa fa-bar-chart-o"></i>
								<span class="nav-label">统计图表</span>
								<span class="fa arrow"></span>
							</a>
							<ul class="nav nav-second-level">
								<li>
									<a class="J_menuItem" href="/search">Date Search</a>
								</li>
								<li>
									<a class="J_menuItem" href="/season_count">Market Season Clothes</a>
								</li>
								<li>
									<a class="J_menuItem" href="/plate_type">Clothing_type Statistics</a>
								</li>
								<li>
									<a class="J_menuItem" href="/detail_echarts">Detail Clothes Echarts</a>
								</li>
							</ul>
						</li>
						<li>
							<a href="#"><i class="fa fa-edit"></i> <span class="nav-label">个人信息</span><span class="fa arrow"></span></a>
							<ul class="nav nav-second-level">
								<li><a class="J_menuItem" href="/profile/?username={{name}}">个人资料</a></li>
								<li><a class="J_menuItem" href="/changeface/?username={{name}}">更换头像</a></li>
							</ul>
						</li>
						<li>
							<a href="#"><i class="fa fa-envelope"></i> <span class="nav-label">信息反馈</span><span class="fa arrow"></span></a>
							<ul class="nav nav-second-level">
								<li><a class="J_menuItem" href="/mail_data/?username={{name}}">发送邮件</a></li>
								<li><a class="J_menuItem" href="/mailbox/?username={{name}}">收件箱</a></li>
								<li><a class="J_menuItem" href="/mail_send/?username={{name}}">已发送</a></li>
							</ul>
						</li>
						<li>
							<a class="J_menuItem" href="/answer"><i class="fa fa-question"></i> <span class="nav-label">疑问解答</span></a>
						</li>
					</ul>
				</div>
			</nav>
			<div id="page-wrapper" class="gray-bg dashbard-1">
				<div class="row border-bottom">
					<nav class="navbar navbar-static-top" role="navigation" style="margin-bottom:0;">
						<div class="navbar-header">
								<a class="navbar-minimalize minimalize-styl-2 btn btn-primary" href="#"><i class="fa fa-bars"></i> </a>
						</div>
					</nav>
				</div>
				<div class="row content-tabs">
					<button class="roll-nav roll-left J_tabLeft"><i class="fa fa-backward"></i></button>
					<nav class="page-tabs J_menuTabs">
						<div class="page-tabs-content">
							<a href="javascript:;" class="active J_menuTab" data-id="welcome.html">首页</a>
						</div>
					</nav>
					<button class="roll-nav roll-right J_tabRight"><i class="fa fa-forward"></i></button>
					<div class="btn-group roll-nav roll-right">
						<button class="dropdown" data-toggle="dropdown">页签操作<span class="caret"></span></button>
						<ul role="menu" class="dropdown-menu dropdown-menu-right">
							<li class="tabCloseCurrent"><a>关闭当前</a></li>
							<li class="J_tabCloseOther"><a>关闭其他</a></li>
							<li class="J_tabCloseAll"><a>全部关闭</a></li>
						</ul>
					</div>
					<a class="roll-nav roll-right tabReload" href="javascript:location.reload();" ><i class="fa fa-refresh"></i> 刷新</a>

				</div>
				<div class="row J_mainContent" id="content-main">
					<iframe class="J_iframe" name="iframe0" width="100%" height="100%" src="/welcome" frameborder="0" seamless></iframe>
				</div>
				<div class="footer">
					<div class="pull-right">&copy; 2023-2025 </div>
				</div>
			</div>
			
		</div>
		<script src="{% static 'js/jquery.min.js' %}"></script>
		<script src="{% static 'js/bootstrap.min.js' %}"></script>
		<script src="{% static 'js/plugins/metisMenu/jquery.metisMenu.js' %}"></script>
		<script src="{% static 'js/plugins/slimscroll/jquery.slimscroll.min.js' %}"></script>
		<script src="{% static 'js/hplus.js' %}"></script>
		<script src="{% static 'js/contabs.js' %}"></script>

		<!-- 添加Layer插件 -->
		<script src="https://cdn.jsdelivr.net/npm/layui@2.8.18/dist/layui.js"></script>
		<script>
		// 初始化layer
		layui.use('layer', function(){
		  window.layer = layui.layer;
		});
		</script>
	</body>
</html>

