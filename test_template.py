#!/usr/bin/env python
import os
import sys
import django
from django.conf import settings

# 添加项目路径
sys.path.insert(0, 'user_portrait-master')

# 设置Django环境变量
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'user_portrait.settings')

# 初始化Django
django.setup()

# 测试模板加载
from django.template.loader import get_template

try:
    template = get_template('index.html')
    print("✅ SUCCESS: 模板加载成功！")
    print("✅ {% load static %} 标签已正确加载")
    print("✅ 模板语法错误已修复")
except Exception as e:
    print(f"❌ ERROR: 模板加载失败: {e}")
    sys.exit(1)

print("\n🎉 Django模板修复验证完成！")
